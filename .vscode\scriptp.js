'use strict';

// Counting digits in a number
function count(n) {
    // Remove 'const' keyword from parameter - can't modify parameters declared as const
    let count = 0;
    
    // Check if n is undefined or not a number
    if (n === undefined || isNaN(n)) {
        return 0;
    }
    
    // Handle negative numbers
    n = Math.abs(n);
    
    // Handle zero as a special case
    if (n === 0) {
        return 1;
    }
    
    while (n > 0) {
        n = Math.floor(n / 10);
        count++;
    }
    return count;
}

// Pass a number to the function
console.log(count(12345));
