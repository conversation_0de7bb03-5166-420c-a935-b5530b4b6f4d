#include <iostream>
#include <iomanip>

using namespace std;

struct student
{
    float id, GPA = 0, final[5], x[5];
    string grade[5];
    string name;
};

int main()
{
    student s1;
    cout << "Enter ID: ";
    cin >> s1.id;
    cout << "Enter Name: ";
    cin >> s1.name;
    cout << "Enter 5 final marks: ";

    float totalPoints = 0; 
    for (int i = 0; i < 5; i++)  
    {
        cin >> s1.final[i];

        
        if (s1.final[i] >= 90 && s1.final[i] <= 100)
        {
            s1.grade[i] = "A+";
            s1.x[i] = 4.0;
        }
        else if (s1.final[i] >= 85 && s1.final[i] < 90)
        {
            s1.grade[i] = "A";
            s1.x[i] = 4.0;
        }
        else if (s1.final[i] >= 80 && s1.final[i] < 85)
        {
            s1.grade[i] = "A-";
            s1.x[i] = 3.75;
        }
        else if (s1.final[i] >= 75 && s1.final[i] < 80)
        {
            s1.grade[i] = "B+";
            s1.x[i] = 3.5;
        }
        else if (s1.final[i] >= 70 && s1.final[i] < 75)
        {
            s1.grade[i] = "B";
            s1.x[i] = 3.0;
        }
        else
        {
            s1.grade[i] = "F";
            s1.x[i] = 0.0;
        }

        totalPoints += s1.x[i] * 3; 
    }

    s1.GPA = totalPoints / 15; 

    // Output details
    cout << "\nDetails:\n";
    cout << "ID " <<  " ";
    cout << "Name " << " ";
    for(int i=1;i<=5;i++){
    cout << "Grade"<<i<<" ";
    }
    
    cout << "\tGPA" << endl;

    cout<<s1.id<<" "<<s1.name<<" ";
    for (int k = 0; k < 5; k++)
    {
        cout << s1.grade[k]<<"\t";
    }

cout<<s1.GPA;
    return 0;
}
