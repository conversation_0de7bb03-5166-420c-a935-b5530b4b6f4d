/* General Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Roboto", sans-serif;
  background-color: #f4f4f9;
  color: #333;
}

header {
  background-color: #4CAF50; /* Header background color */
  color: white; /* Header text color */
  padding: 20px 0; /* Padding for header */
  text-align: center; /* Center align header text */
}

/* Main Content */
main {
  display: flex; /* Flexbox layout for main content */
  justify-content: space-between; /* Space between products and cart */
  margin: 20px; /* Margin around main content */
}

/* Products Section */
#products {
  flex: 1; /* Allow products section to take available space */
  margin-right: 20px; /* Right margin for spacing */
}

#products div {
  background-color: white; /* Background color for product cards */
  border: 1px solid #ddd; /* Border for product cards */
  border-radius: 5px; /* Rounded corners */
  padding: 15px; /* Padding inside product cards */
  margin-bottom: 15px; /* Space between product cards */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Shadow for depth */
  transition: transform 0.2s; /* Smooth transition for hover effect */
}

#products div:hover {
  transform: scale(1.05); /* Slightly scale up on hover */
}

#products h3 {
  margin: 0 0 10px; /* Margin for product title */
}

#products button {
  background-color: #4CAF50; /* Button background color */
  color: white; /* Button text color */
  border: none; /* No border */
  padding: 10px; /* Padding for button */
  cursor: pointer; /* Pointer cursor on hover */
  border-radius: 5px; /* Rounded corners */
  transition: background-color 0.3s; /* Smooth transition for hover effect */
}

#products button:hover {
  background-color: #45a049; /* Darker green on hover */
}

/* Cart Section */
#cart {
  flex: 1; /* Allow cart section to take available space */
  background-color: white; /* Background color for cart */
  border: 1px solid #ddd; /* Border for cart */
  border-radius: 5px; /* Rounded corners */
  padding: 15px; /* Padding inside cart */
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1); /* Shadow for depth */
}

#cart h2 {
  margin: 0 0 15px; /* Margin for cart title */
}

#cart-items {
  list-style-type: none; /* Remove bullet points */
  padding: 0; /* Remove padding */
  margin: 0; /* Remove margin */
}

#cart-items li {
  display: flex; /* Flexbox for item layout */
  justify-content: space-between; /* Space between item name and price */
  padding: 10px 0; /* Padding for cart items */
  border-bottom: 1px solid #ddd; /* Border between items */
}

#total-cost {
  font-weight: bold; /* Bold text for total */
  margin-top: 10px; /* Margin above total */
}

/* Checkout Button */
#checkout {
  background-color: #4CAF50; /* Checkout button background color */
  color: white; /* Checkout button text color */
  border: none; /* No border */
  padding: 10px; /* Padding for button */
  cursor: pointer; /* Pointer cursor on hover */
  border-radius: 5px; /* Rounded corners */
  width: 100%; /* Full width button */
  transition: background-color 0.3s; /* Smooth transition for hover effect */
}

#checkout:hover {
  background-color: #45a049; /* Darker green on hover */
}

/* Responsive Design */
@media (max-width: 768px) {
  main {
      flex-direction: column; /* Stack sections on smaller screens */
  }

  #products {
      margin-right: 0; /* Remove right margin on smaller screens */
  }

  #cart {
      margin-top: 20px; /* Add spacing between sections */
  }
}
