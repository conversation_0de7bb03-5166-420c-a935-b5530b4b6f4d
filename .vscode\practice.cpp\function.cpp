#include <iostream>

using namespace std;

int largest(int x, int y, int z,int max) {
    if (x >= y && x >= z) {
       max=x;
        return max;
    } else if (y >= x && y >= z) {
       max=y;
        return max;
    } else {
       max= z;
       return max;
    }
}

 int smallest(int a, int b, int c, int &min) {
    if (a <= b && a <= c) {
      min=a;
      return min;
    } else if (b <= a && b <= c) {
      min=b;
      return min;
    } else {
       min=c;
       return min;
    }
}


void display(int n1, int n2, int n3,int max, int &min) {
    max= largest(n1, n2, n3,max);
    int sm=smallest(n1,n2,n3,min);
    cout << "The largest number is: " << max << endl;
    cout << "the smallest number is: " << min << endl;
}
int main() {
    int n1, n2, n3,max,min;
    cout << "Enter three numbers: ";
    cin >> n1 >> n2 >> n3;
    display(n1, n2, n3,max,min);
   
 return 0;
 }

// void findLargestSmallest(int a, int b, int c, int &largest, int &smallest) {
//     if (a >= b && a >= c) {
//         largest = a;
//     } else if (b >= a && b >= c) {
//         largest = b;
//     } else {
//         largest = c;
//     }

//     if (a <= b && a <= c) {
//         smallest = a;
//     } else if (b <= a && b <= c) {
//         smallest = b;
//     } else {
//         smallest = c;
//     }
// }

// int main() {
//     int n1, n2, n3, max, min;

//     cout << "Enter three numbers: ";
//     cin >> n1 >> n2 >> n3;

//     findLargestSmallest(n1, n2, n3, max, min);  // Updates max and min directly

//     cout << "Largest number: " << max << endl;
//     cout << "Smallest number: " << min << endl;

//     return 0;
// }
